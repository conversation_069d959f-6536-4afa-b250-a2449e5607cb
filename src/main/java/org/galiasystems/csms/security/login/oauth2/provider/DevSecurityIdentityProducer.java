package org.galiasystems.csms.security.login.oauth2.provider;

import org.galiasystems.csms.management.model.User;
import org.galiasystems.csms.security.GraphqlAuthMechanism;

import io.quarkus.arc.profile.IfBuildProfile;
import io.quarkus.security.identity.SecurityIdentity;
import io.quarkus.security.runtime.QuarkusPrincipal;
import io.quarkus.security.runtime.QuarkusSecurityIdentity;
import jakarta.annotation.Priority;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.inject.Alternative;
import jakarta.enterprise.inject.Produces;
import jakarta.inject.Singleton;

/**
 * Provides a default security identity for development mode.
 * This allows CsmsServiceBase.getCurrentUser() to return a valid user
 * without requiring authentication in dev mode.
 */
@Singleton
@IfBuildProfile("dev")
public class DevSecurityIdentityProducer {

    @Produces
    @Alternative
    @Priority(1)
    @ApplicationScoped
    public SecurityIdentity produceDevSecurityIdentity() {
        // Create a test user matching the globaladmin user from database migration
        User devUser = new User(-3L, null, "globaladmin", null);
        devUser.addRoleGlobalAdmin();
        
        return QuarkusSecurityIdentity.builder()
                .setPrincipal(new QuarkusPrincipal("globaladmin"))
                .addRole("GlobalAdmin")
                .addAttribute(GraphqlAuthMechanism.SECURITY_IDENTITY_ATTRIBUTE_NAME_USER, devUser)
                .build();
    }
}
