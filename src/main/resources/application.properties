quarkus.native.builder-image=quay.io/quarkus/ubi-quarkus-mandrel-builder-image:jdk-24

###########################
###### Common configs #####
###########################
quarkus.http.host=0.0.0.0
quarkus.http.port=8888
quarkus.http.cors=true
quarkus.container-image.image=smart-charge-backend
quarkus.websockets-next.server.auto-ping-interval=2

quarkus.hibernate-orm.physical-naming-strategy=org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy

hibernate.reactive.panache.sessionOnDemandOpened=true
# GraphQL configuration
quarkus.smallrye-graphql.show-runtime-exception-message=java.lang.RuntimeException
# Security
quarkus.http.auth.basic=true
quarkus.ssl.native=true
quarkus.http.ssl-port=8443

# TODO use tls registry: https://quarkus.io/guides/tls-registry-reference
quarkus.http.ssl.certificate.key-store-file=META-INF/resources/server.keystore
quarkus.http.ssl.certificate.key-store-password=password

# none, request, required
quarkus.http.ssl.client-auth=request
quarkus.http.insecure-requests=enabled
quarkus.http.ssl.certificate.trust-store-file=META-INF/resources/server.truststore
quarkus.http.ssl.certificate.trust-store-password=password
quarkus.native.additional-build-args=-J-Djavax.net.ssl.trustStore=D:/devtools/project/charger/smart-charge/src/main/resources/META-INF/resources/client.truststore,-J-Djavax.net.ssl.trustStorePassword=password

# OAuth2
#quarkus.oidc-client.credentials.secret=secret
quarkus.oidc-client.grant.type=code
#quarkus.http.auth.permission.authenticated.paths=/*
#quarkus.http.auth.permission.authenticated.policy=deny
#quarkus.smallrye-jwt.enabled=true

# CSMS
csms.oauth2.userTokens.cookie.sessionId.name=SESSION_ID
csms.oauth2.userTokens.cookie.accessToken.name=ACCESS_TOKEN
csms.oauth2.userTokens.cookie.sameSite=LAX
csms.oauth2.userTokens.cookie.domain=localhost
csms.oauth2.userTokens.cookie.secure=true
csms.oauth2.userTokens.cookie.httpOnly=true
csms.oauth2.userTokens.cookie.path=/
csms.oauth2.authorization.endpoint=http://localhost:8180/realms/flutter-app/protocol/openid-connect/auth/
csms.oauth2.registrations.endpoint=http://localhost:8180/realms/flutter-app/protocol/openid-connect/registrations/

###########################
###### Dev profile ########
###########################
# Datasource configs
%dev.quarkus.datasource.devservices.port=53277

%dev.quarkus.log.category."org.galiasystems".level=DEBUG
# %dev.quarkus.log.level=DEBUG
# %dev.quarkus.http.access-log.enabled=true

%dev.quarkus.http.cors.origins=/.*/
%dev.quarkus.http.cors.access-control-allow-credentials=true

# Hibernate configs
%dev.quarkus.hibernate-orm.log.sql=true
%dev.quarkus.hibernate-orm.database.generation=none
%dev.quarkus.hibernate-orm.sql-load-script=no-file

# Flyway config
%dev.quarkus.flyway.enabled=true
%dev.quarkus.flyway.migrate-at-start=true
%dev.quarkus.flyway.clean-at-start=true

# OAuth2
%dev.quarkus.oidc-client.auth-server-url=http://localhost:8180/realms/flutter-app/
%dev.quarkus.oidc-client.client-id=flutter-client
%dev.quarkus.oidc-client.credentials.secret=63vMfP5b0xa8Pjx6KbNquYqggP6Z76jC
# secret on oci environment
#%dev.quarkus.oidc-client.credentials.secret=roYrOkcqmMJscLp8BeqeiGHdvw8Hp7o1
#%dev.quarkus.oidc.auth-server-url=http://localhost:8180/realms/flutter-app/
%dev.quarkus.rest-client.custom-keycloak.url=http://localhost:8180/realms/flutter-app
%dev.quarkus.keycloak.devservices.enabled=true

# CSMS
%dev.csms.oauth2.userTokens.cookie.sessionId.name=SESSION_ID
%dev.csms.oauth2.userTokens.cookie.accessToken.name=ACCESS_TOKEN
%dev.csms.oauth2.userTokens.cookie.sameSite=LAX
%dev.csms.oauth2.userTokens.cookie.domain=localhost
%dev.csms.oauth2.userTokens.cookie.secure=true
%dev.csms.oauth2.userTokens.cookie.httpOnly=true
%dev.csms.oauth2.userTokens.cookie.path=/

# Mock OidcClient and Dev Security Identity
%dev.quarkus.arc.selected-alternatives=org.galiasystems.csms.security.login.oauth2.MockOidcClient,org.galiasystems.csms.security.login.oauth2.provider.DevSecurityIdentityProducer

%dev.csms.oauth2.authorization.endpoint=http://localhost:8180/realms/flutter-app/protocol/openid-connect/auth/
%dev.csms.oauth2.registrations.endpoint=http://localhost:8180/realms/flutter-app/protocol/openid-connect/registrations/

###########################
###### Test profile #######
###########################
# Datasource configs
%test.quarkus.datasource.devservices.port=53277
%test.quarkus.http.host=0.0.0.0
%test.quarkus.http.port=8081

# Hibernate configs
%test.quarkus.hibernate-orm.log.sql=true
%test.quarkus.hibernate-orm.database.generation=none
%test.quarkus.hibernate-orm.sql-load-script=no-file
# Mock OidcClient
%test.quarkus.arc.selected-alternatives=org.galiasystems.csms.security.login.oauth2.MockOidcClient
# CSMS
%test.csms.oauth2.userTokens.cookie.sessionId.name=SESSION_ID
%test.csms.oauth2.userTokens.cookie.accessToken.name=ACCESS_TOKEN
%test.csms.oauth2.userTokens.cookie.sameSite=LAX
%test.csms.oauth2.userTokens.cookie.domain=localhost
%test.csms.oauth2.userTokens.cookie.secure=true
%test.csms.oauth2.userTokens.cookie.httpOnly=true
%test.csms.oauth2.userTokens.cookie.path=/
# OAuth2
%test.quarkus.rest-client.custom-keycloak.url=http://localhost:8180/realms/flutter-app

# Flyway config
%test.quarkus.flyway.enabled=true
%test.quarkus.flyway.clean-at-start=true
%test.quarkus.flyway.migrate-at-start=true

###########################
###### Prod profile #######
###########################
%prod.quarkus.datasource.db-kind=postgresql
%prod.quarkus.datasource.username=smart_charge
%prod.quarkus.datasource.password=smart_charge
%prod.quarkus.log.level=DEBUG

# Reactive config
%prod.quarkus.datasource.reactive.url=vertx-reactive:postgresql://postgres/smart_charge?currentSchema=smart_charge
%prod.quarkus.datasource.reactive.max-size=20

# Flyway config
%prod.quarkus.flyway.enabled=true
%prod.quarkus.flyway.migrate-at-start=true

# JDBC config
%prod.quarkus.datasource.jdbc.url=******************************************************************

%prod.quarkus.hibernate-orm.database.generation = none
%prod.quarkus.hibernate-orm.sql-load-script = no-file
%prod.quarkus.http.cors.origins=/.*/
%prod.quarkus.http.cors.access-control-allow-credentials=true

# OAuth2
%prod.quarkus.oidc-client.auth-server-url=${QUARKUS_OIDC_CLIENT_AUTH_SERVER_URL}
%prod.quarkus.oidc-client.client-id=flutter-client
%prod.quarkus.oidc-client.credentials.secret=${QUARKUS_OIDC_CLIENT_CREDENTIALS_SECRET}
%prod.quarkus.rest-client.custom-keycloak.url=${QUARKUS_REST_CLIENT_CUSTOM_KEYCLOAK_URL}



###########################
###### Samples      #######
###########################


# Run Flyway migrations automatically
# quarkus.flyway.migrate-at-start=true

#manually by injecting the Flyway object and calling Flyway#repair().
# quarkus.flyway.repair-at-start=true

# More Flyway configuration options
# quarkus.flyway.baseline-on-migrate=true
# quarkus.flyway.baseline-version=1.0.0
# quarkus.flyway.baseline-description=Initial version
# quarkus.flyway.connect-retries=10
# quarkus.flyway.schemas=TEST_SCHEMA
# quarkus.flyway.table=flyway_quarkus_history
# quarkus.flyway.locations=db/location1,db/location2
# quarkus.flyway.sql-migration-prefix=X
# quarkus.flyway.repeatable-sql-migration-prefix=K
# %dev.quarkus.flyway.clean-at-start=true

# %dev-with-data.quarkus.hibernate-orm.database.generation = update
# %dev-with-data.quarkus.hibernate-orm.sql-load-script = no-file