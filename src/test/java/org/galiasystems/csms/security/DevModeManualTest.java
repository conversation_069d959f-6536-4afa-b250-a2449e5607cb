package org.galiasystems.csms.security;

import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.junit.TestProfile;
import jakarta.inject.Inject;
import org.galiasystems.csms.management.csms.base.CsmsServiceBase;
import org.galiasystems.csms.management.model.User;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Simple manual test to verify DevSecurityIdentityProducer works.
 * This test simulates dev mode by setting the profile to dev.
 */
@QuarkusTest
@TestProfile(DevModeManualTest.DevModeProfile.class)
public class DevModeManualTest {

    /**
     * Simple test service that extends CsmsServiceBase
     */
    public static class TestService extends CsmsServiceBase {
        public User getCurrentUserForTest() {
            return getCurrentUser();
        }
        
        public String getCurrentUserNameForTest() {
            return getCurrentUserUserName();
        }
        
        public boolean isGlobalAdminForTest() {
            return isCurrentUserGlobalAdmin();
        }
    }

    @Inject
    TestService testService;

    @Test
    @DisplayName("Dev mode should provide globaladmin user automatically")
    public void testDevModeAutoLogin() {
        // Test that getCurrentUser returns the dev user
        User currentUser = testService.getCurrentUserForTest();
        
        assertNotNull(currentUser, "Current user should not be null in dev mode");
        assertEquals("globaladmin", currentUser.getUserName(), "User should be globaladmin");
        assertEquals(-3L, currentUser.getId(), "User ID should match database migration");
        assertTrue(testService.isGlobalAdminForTest(), "User should be global admin");
        
        System.out.println("✓ Dev mode auto-login working!");
        System.out.println("  User: " + currentUser.getUserName());
        System.out.println("  ID: " + currentUser.getId());
        System.out.println("  Is Global Admin: " + testService.isGlobalAdminForTest());
    }

    /**
     * Test profile that simulates dev mode
     */
    public static class DevModeProfile implements io.quarkus.test.junit.QuarkusTestProfile {
        @Override
        public java.util.Map<String, String> getConfigOverrides() {
            return java.util.Map.of(
                // Set profile to dev to activate our DevSecurityIdentityProducer
                "quarkus.profile", "dev"
            );
        }
    }
}
