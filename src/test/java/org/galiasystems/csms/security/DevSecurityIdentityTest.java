package org.galiasystems.csms.security;

import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.junit.TestProfile;
import jakarta.inject.Inject;
import org.galiasystems.csms.management.csms.base.CsmsServiceBase;
import org.galiasystems.csms.management.model.User;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test to verify that the DevSecurityIdentityProducer works correctly in dev mode.
 * This test uses a custom test profile that simulates dev mode behavior.
 */
@QuarkusTest
@TestProfile(DevSecurityIdentityTest.DevModeTestProfile.class)
public class DevSecurityIdentityTest {

    /**
     * Test service that extends CsmsServiceBase to test getCurrentUser functionality
     */
    public static class TestCsmsService extends CsmsServiceBase {
        public User getTestCurrentUser() {
            return getCurrentUser();
        }
        
        public String getTestCurrentUserName() {
            return getCurrentUserUserName();
        }
        
        public boolean getTestIsCurrentUserGlobalAdmin() {
            return isCurrentUserGlobalAdmin();
        }
    }

    @Inject
    TestCsmsService testService;

    @Test
    @DisplayName("Should provide globaladmin user in dev mode")
    public void testDevModeUserIsAvailable() {
        // Test that getCurrentUser returns a valid user
        User currentUser = testService.getTestCurrentUser();
        assertNotNull(currentUser, "Current user should not be null in dev mode");
        assertEquals("globaladmin", currentUser.getUserName(), "User should be globaladmin");
        assertEquals(-3L, currentUser.getId(), "User ID should match database migration");
        assertNull(currentUser.getTenantId(), "Global admin should have null tenant ID");
    }

    @Test
    @DisplayName("Should have GlobalAdmin role in dev mode")
    public void testDevModeUserHasGlobalAdminRole() {
        // Test that the user has GlobalAdmin role
        assertTrue(testService.getTestIsCurrentUserGlobalAdmin(), "User should be global admin");
        assertEquals("globaladmin", testService.getTestCurrentUserName(), "Username should be globaladmin");
    }

    /**
     * Custom test profile that simulates dev mode by setting the build profile to "dev"
     */
    public static class DevModeTestProfile implements io.quarkus.test.junit.QuarkusTestProfile {
        @Override
        public java.util.Map<String, String> getConfigOverrides() {
            return java.util.Map.of(
                "quarkus.profile", "dev",
                // Enable our dev security identity producer
                "quarkus.arc.selected-alternatives", 
                "org.galiasystems.csms.security.login.oauth2.MockOidcClient,org.galiasystems.csms.security.login.oauth2.provider.DevSecurityIdentityProducer"
            );
        }
    }
}
